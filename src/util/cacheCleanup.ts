/**
 * ahooks useRequest 缓存清理工具
 * 解决 @ahooksjs/use-request v2.8.10 缓存无法清理导致的内存泄漏问题
 */

// 尝试动态导入 ahooks 内部缓存模块
let ahooksCacheModule: any = null;
let ahooksCacheMap: Map<string, any> | null = null;

/**
 * 初始化 ahooks 缓存清理器
 */
export function initAhooksCacheCleanup() {
  try {
    // 方法1：尝试直接访问内部缓存模块
    ahooksCacheModule = require('@ahooksjs/use-request/lib/utils/cache');
    console.log('Successfully loaded ahooks cache module:', ahooksCacheModule);
    
    // 方法2：尝试通过反射获取内部缓存 Map
    // ahooks 内部使用一个全局 Map 对象存储缓存
    const moduleCache = require.cache;
    const cacheModulePath = require.resolve('@ahooksjs/use-request/lib/utils/cache');
    const cacheModuleExports = moduleCache[cacheModulePath]?.exports;
    
    if (cacheModuleExports) {
      // 通过闭包访问内部的 cache Map
      // 这需要一些技巧，因为 cache 是私有变量
      console.log('Cache module exports:', Object.keys(cacheModuleExports));
    }
    
  } catch (error) {
    console.warn('Failed to load ahooks cache module:', error);
  }
}

/**
 * 获取所有缓存键
 */
export function getAllCacheKeys(): string[] {
  const keys: string[] = [];
  
  try {
    // 如果能访问到缓存模块，尝试获取所有键
    if (ahooksCacheModule && typeof ahooksCacheModule.getCache === 'function') {
      // getCache 只能获取单个键，我们需要找到存储所有键的地方
      console.log('Available cache methods:', Object.keys(ahooksCacheModule));
    }
    
    // 方法2：尝试通过全局变量访问
    if (typeof window !== 'undefined') {
      const possibleGlobalNames = [
        '__AHOOKS_CACHE__',
        '__USE_REQUEST_CACHE__', 
        '__AHOOKS_USE_REQUEST_CACHE__',
        '_ahooks_cache_',
        '__ahooks_internal_cache__'
      ];
      
      for (const name of possibleGlobalNames) {
        const globalCache = (window as any)[name];
        if (globalCache instanceof Map) {
          console.log(`Found cache at window.${name}:`, globalCache);
          globalCache.forEach((_, key) => keys.push(key));
          break;
        }
      }
    }
    
  } catch (error) {
    console.warn('Failed to get cache keys:', error);
  }
  
  return keys;
}

/**
 * 清理与指定键相关的缓存
 */
export function clearRelatedCache(targetKeys: string[]): number {
  let clearedCount = 0;
  
  try {
    // 获取所有缓存键
    const allKeys = getAllCacheKeys();
    console.log('All cache keys:', allKeys);
    
    // 找到需要清理的键
    const keysToDelete = allKeys.filter(key => 
      targetKeys.some(targetKey => 
        key.includes(targetKey) || 
        key.includes('executeSqlStatement') ||
        key.includes('queryExecuteResults') ||
        key.includes('fetchExplain')
      )
    );
    
    console.log('Keys to delete:', keysToDelete);
    
    // 尝试清理这些键
    if (ahooksCacheModule && typeof ahooksCacheModule.setCache === 'function') {
      keysToDelete.forEach(key => {
        try {
          // 设置一个空缓存并立即过期
          ahooksCacheModule.setCache(key, 0, null);
          clearedCount++;
        } catch (e) {
          console.warn(`Failed to clear cache for key ${key}:`, e);
        }
      });
    }
    
  } catch (error) {
    console.warn('Failed to clear related cache:', error);
  }
  
  return clearedCount;
}

/**
 * 强制清理所有 ahooks 缓存
 * 这是一个更激进的清理方式
 */
export function clearAllAhooksCache(): boolean {
  try {
    // 方法1：如果能访问缓存模块，尝试重置整个缓存
    if (ahooksCacheModule) {
      // 由于没有直接的清理所有缓存的方法，我们尝试其他技巧
      console.log('Attempting to clear all ahooks cache...');
    }
    
    // 方法2：通过模块缓存清理
    const moduleCache = require.cache;
    const cacheModulePath = require.resolve('@ahooksjs/use-request/lib/utils/cache');
    
    if (moduleCache[cacheModulePath]) {
      // 删除模块缓存，强制重新加载
      delete moduleCache[cacheModulePath];
      console.log('Cleared ahooks cache module from require cache');
      
      // 重新初始化
      initAhooksCacheCleanup();
      return true;
    }
    
  } catch (error) {
    console.warn('Failed to clear all ahooks cache:', error);
  }
  
  return false;
}

/**
 * Monkey Patch useRequest 来跟踪所有实例
 */
let requestInstances: Map<string, any> = new Map();

export function trackUseRequestInstance(key: string, instance: any) {
  requestInstances.set(key, instance);
  console.log(`Tracking useRequest instance: ${key}`);
}

export function cleanupTrackedInstances(targetKeys: string[]): number {
  let clearedCount = 0;
  
  requestInstances.forEach((instance, key) => {
    const shouldCleanup = targetKeys.some(targetKey => 
      key.includes(targetKey)
    );
    
    if (shouldCleanup) {
      try {
        // 尝试取消请求
        if (instance && typeof instance.cancel === 'function') {
          instance.cancel();
        }
        
        // 清理实例
        requestInstances.delete(key);
        clearedCount++;
        console.log(`Cleaned up useRequest instance: ${key}`);
      } catch (e) {
        console.warn(`Failed to cleanup instance ${key}:`, e);
      }
    }
  });
  
  return clearedCount;
}

// 初始化
if (typeof window !== 'undefined') {
  initAhooksCacheCleanup();
}