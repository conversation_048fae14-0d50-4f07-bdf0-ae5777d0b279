import { AnyCnameRecord } from 'dns';
import { CellRenderResultData } from 'src/api';
import { CELL_TYPE } from 'src/pageTabs/queryPage/resultTabs/resultContentGrid/constants';


//16进制字符串转化为二进制
export const transferHexStringToOtherTypes = (hexString: string, type:CELL_TYPE ) => {
  if (hexString.startsWith('0x')) {
    hexString = hexString.match(/^0x(\S*)/)?.[1] || '';
  }
  //目前字符串默认添加
  hexString = hexString.replace(/[^A-Fa-f0-9]/g, '')
 
  if (hexString.length % 2) {
    // error: cleaned hex string length is odd
    return
  }

  const binary = []
  for (let i = 0; i < hexString.length / 2; i++) {
    const h = hexString.substr(i * 2, 2)
    binary[i] = parseInt(h, 16)
  }
  //解析为二进制
  const byteArray = new Uint8Array(binary);

  let str: any = hexString;

  switch(type) {
    case 'GBK':
    default:
      const decoder = new TextDecoder(type);
      // 将 Uint8Array 转换为 UTF-8 字符串
      str = decoder.decode(byteArray);
      break;
  }
  return str;
}
//方法一
export const getImageSrcFromHex = (hexString: string) => {
  if (hexString.startsWith('0x')) {
    hexString = hexString.match(/^0x(\S*)/)?.[1] || '';
  }
  //目前字符串默认添加
  hexString = hexString.replace(/[^A-Fa-f0-9]/g, '')
 
  if (hexString.length % 2) {
    // error: cleaned hex string length is odd
    return
  }

  const binary = []
  for (let i = 0; i < hexString.length / 2; i++) {
    const h = hexString.substr(i * 2, 2)
    binary[i] = parseInt(h, 16)
  }
  //解析为二进制
  const byteArray = new Uint8Array(binary)

  return window.URL.createObjectURL(
    new Blob([byteArray], { type: 'application/octet-stream' }),
  )
}
//方法二
export const HexToImage = (hexString: string) => {
  if (hexString.length % 2) {
    return ''
  }
  // 单元格 16进制展示时字符串前默认添加了0x
  if (hexString.startsWith('0x')) {
    hexString = hexString.match(/^0x(\S*)/)?.[1] || '';
  }

  let bString = "";
  for( var i = 0; i < hexString?.length; i +=2) {
    //将16进制转化为10进制 并生成对应的字符串；
      bString += String.fromCharCode( parseInt( hexString.substr( i, 2), 16));
  }
    //btoa转化为base64
  return 'data:image/png;base64,' + btoa(bString);
}

/**
 * format execute reasult
 */
// export const formatExecuteResult = (res: CellRenderResultData[]) => {
//   const binaryKeyCache: any = {};

//   return res.map((queryResult) => {
//     const { resultData } = queryResult;
//     const plainResultData = resultData.map((result) => {
//       if (typeof result !== 'object') return result;
//       const plainResult: { [key: string | symbol]: any } = {};
//       const keys = Object.keys(result);

//       for (let key of keys) {
//         const value = result[key].value;
//         plainResult[key] = value;

//         // 使用缓存来存储已经计算过的结果
//         if (!binaryKeyCache[key]) {
//           binaryKeyCache[key] = result[key].renderType?.includes('binary');
//         }

//         if (result[key].formatValue) {
//           plainResult[`get${key}`] = () => result[key].formatValue;
//         }

//         if (binaryKeyCache[key] && result[key]?.size) {
//           plainResult[`${key}CQSize`] = result[key].size;
//         }
//       }

//       return plainResult;
//     });

//     // const cleanData = JSON.parse(JSON.stringify(resultData));
//     return {
//       ...queryResult,
//       resultData: plainResultData,
//       detailedResultData: resultData,
//     };
//   });
// };

/**
 * 深度净化数据，完全断开所有引用链
 * 使用多重策略确保内存引用被彻底清理
 */
function deepSanitizeData(data: any): any {
  if (data === null || data === undefined) {
    return data;
  }
  
  // 处理基本类型
  if (typeof data !== 'object') {
    return data;
  }
  
  // 处理日期对象
  if (data instanceof Date) {
    return new Date(data.getTime());
  }
  
  // 处理数组
  if (Array.isArray(data)) {
    return data.map(item => deepSanitizeData(item));
  }
  
  // 处理对象 - 完全重建，断开所有引用
  const sanitized: any = {};
  
  try {
    // 只处理可枚举的自有属性，避免原型链污染
    Object.keys(data).forEach(key => {
      const value = data[key];
      
      // 跳过函数和 symbol，它们无法被序列化
      if (typeof value === 'function' || typeof value === 'symbol') {
        return;
      }
      
      // 递归净化嵌套对象
      sanitized[key] = deepSanitizeData(value);
    });
    
    return sanitized;
  } catch (e) {
    console.warn("Deep sanitization failed for object, falling back to JSON method:", e);
    // 降级方案：使用 JSON 序列化
    try {
      return JSON.parse(JSON.stringify(data));
    } catch (jsonError) {
      console.error("JSON sanitization also failed:", jsonError);
      return null; // 返回 null 而不是原始数据，避免引用泄漏
    }
  }
}

/**
 * 优化后的格式化执行结果函数
 * 彻底解决内存泄漏问题，断开所有引用链
 */
export const formatExecuteResult = (res: CellRenderResultData[]) => {
  return res.map((queryResult) => {
    const { resultData } = queryResult;
    
    // 步骤1: 深度净化原始数据，创建完全独立的副本
    const cleanDetailedResultData = deepSanitizeData(resultData);
    
    // 步骤2: 从净化后的数据创建轻量级显示数据
    // 注意：不再创建任何函数或闭包，避免引用泄漏
    const plainResultData = cleanDetailedResultData?.map((result: any) => {
      if (!result || typeof result !== 'object') {
        return result;
      }
      
      const plainResult: { [key: string]: any } = {};
      
      // 只处理对象的自有属性
      Object.keys(result).forEach(key => {
        const cellData = result[key];
        
        // 直接提取值，不创建任何函数引用
        if (cellData && typeof cellData === 'object' && 'value' in cellData) {
          plainResult[key] = cellData.value;
          
          // 如果有格式化值，直接存储而不是创建函数
          if ('formatValue' in cellData && cellData.formatValue !== undefined) {
            plainResult[`${key}FormatValue`] = cellData.formatValue;
          }
          
          // 存储渲染类型和大小信息（如果需要）
          if ('renderType' in cellData) {
            plainResult[`${key}RenderType`] = cellData.renderType;
          }
          
          if ('size' in cellData) {
            plainResult[`${key}Size`] = cellData.size;
          }
        } else {
          plainResult[key] = cellData;
        }
      });
      
      return plainResult;
    }) || [];
    
    // 步骤3: 净化外层 queryResult 对象
    const cleanQueryResult = deepSanitizeData(queryResult);
    
    // 步骤4: 返回完全干净的对象，无任何原始引用
    return {
      ...cleanQueryResult,
      resultData: plainResultData,
      detailedResultData: cleanDetailedResultData,
    };
  });
};

export const getLimitedString = (
  data: Object | string | undefined,
  limitedLength: number = 50,
) => {
  if (!data) return ''
  const stringifyData = typeof data === 'string' ? data : JSON.stringify(data)
  if (!stringifyData) return ''
  const length = stringifyData.length
  return length > limitedLength
    ? `${stringifyData.slice(0, limitedLength)}...`
    : stringifyData
}

export const getColLimitLength = (colNum: number) => {
  const ROW_MAX_STRING_NUMBER = 1000
  const Col_MIN_STRING_NUMBER = 50
  return Col_MIN_STRING_NUMBER > ROW_MAX_STRING_NUMBER / colNum
    ? Col_MIN_STRING_NUMBER
    : Math.floor(ROW_MAX_STRING_NUMBER / colNum)
}

/** transform hex to string */
export const hexToString = (hex: string): string => {
  // 后端传过来的是不带空格的十六进制字符串
  if (!!hex) {
    let hexArray: string[] = []
    const length = hex.length
    for (let i = 0; i < length; i += 2) {
      hexArray.push(hex.slice(i, i + 2))
    }
    const hexArrayNumber = hexArray.map((item) => {
      return Number('0x' + item)
    })
    return String.fromCharCode(...hexArrayNumber)
  } else {
    return '??'
  }
}

// transfer string to hex
export const stringToHex = (str: string | number): string | number => {
  if(typeof(str) === 'number'){
    return str.toString(16)
  }
  if (str === '') return ''
  const hexCharCode = []
  // hexCharCode.push('0x')
  for (var i = 0; i < str.length; i++) {
    hexCharCode.push(str.charCodeAt(i).toString(16))
  }
  return hexCharCode.join('')
}

/**
 * 安全地处理行数据，完全避免内存泄漏
 * 彻底移除闭包函数，使用直接值存储替代
 */
export const handleRowDataWithEditableSafe = (data: any[]) => {
  return data.map((curItem: any) => {
    const customCurItem: any = { editable: true };
    const keys = Object.keys(curItem);

    for (const key of keys) {
      const value = curItem[key];

      // 处理 cursor 值的特殊情况
      if (value?.cursorValue !== undefined) {
        // 深拷贝 cursor 对象，避免引用泄漏
        customCurItem[key] = deepSanitizeData(value);
      } else if (value && typeof value === 'object' && 'value' in value) {
        // 标准 CellData 对象处理
        customCurItem[key] = value.value;
        
        // 直接存储格式化值，不创建函数
        if ('formatValue' in value && value.formatValue !== undefined) {
          customCurItem[`${key}FormatValue`] = value.formatValue;
          
          // 完全移除 getter 函数！改用直接属性访问
          // 旧代码: customCurItem[`get${key}`] = () => formatValueCopy;
          // 新代码: 使用 `${key}FormatValue` 属性直接访问
        }
        
        // 存储其他元数据
        if ('renderType' in value) {
          customCurItem[`${key}RenderType`] = value.renderType;
        }
        
        if ('size' in value) {
          customCurItem[`${key}CQSize`] = value.size;
        }
        
        if ('editable' in value && !value.editable) {
          customCurItem.editable = false;
        }
      } else {
        // 直接值
        customCurItem[key] = value;
      }
    }

    return customCurItem;
  });
}
