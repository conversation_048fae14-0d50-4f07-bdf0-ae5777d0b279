import { AnyCnameRecord } from 'dns';
import { CellRenderResultData } from 'src/api';
import { CELL_TYPE } from 'src/pageTabs/queryPage/resultTabs/resultContentGrid/constants';


//16进制字符串转化为二进制
export const transferHexStringToOtherTypes = (hexString: string, type:CELL_TYPE ) => {
  if (hexString.startsWith('0x')) {
    hexString = hexString.match(/^0x(\S*)/)?.[1] || '';
  }
  //目前字符串默认添加
  hexString = hexString.replace(/[^A-Fa-f0-9]/g, '')
 
  if (hexString.length % 2) {
    // error: cleaned hex string length is odd
    return
  }

  const binary = []
  for (let i = 0; i < hexString.length / 2; i++) {
    const h = hexString.substr(i * 2, 2)
    binary[i] = parseInt(h, 16)
  }
  //解析为二进制
  const byteArray = new Uint8Array(binary);

  let str: any = hexString;

  switch(type) {
    case 'GBK':
    default:
      const decoder = new TextDecoder(type);
      // 将 Uint8Array 转换为 UTF-8 字符串
      str = decoder.decode(byteArray);
      break;
  }
  return str;
}
//方法一
export const getImageSrcFromHex = (hexString: string) => {
  if (hexString.startsWith('0x')) {
    hexString = hexString.match(/^0x(\S*)/)?.[1] || '';
  }
  //目前字符串默认添加
  hexString = hexString.replace(/[^A-Fa-f0-9]/g, '')
 
  if (hexString.length % 2) {
    // error: cleaned hex string length is odd
    return
  }

  const binary = []
  for (let i = 0; i < hexString.length / 2; i++) {
    const h = hexString.substr(i * 2, 2)
    binary[i] = parseInt(h, 16)
  }
  //解析为二进制
  const byteArray = new Uint8Array(binary)

  return window.URL.createObjectURL(
    new Blob([byteArray], { type: 'application/octet-stream' }),
  )
}
//方法二
export const HexToImage = (hexString: string) => {
  if (hexString.length % 2) {
    return ''
  }
  // 单元格 16进制展示时字符串前默认添加了0x
  if (hexString.startsWith('0x')) {
    hexString = hexString.match(/^0x(\S*)/)?.[1] || '';
  }

  let bString = "";
  for( var i = 0; i < hexString?.length; i +=2) {
    //将16进制转化为10进制 并生成对应的字符串；
      bString += String.fromCharCode( parseInt( hexString.substr( i, 2), 16));
  }
    //btoa转化为base64
  return 'data:image/png;base64,' + btoa(bString);
}

/**
 * format execute reasult
 */
// export const formatExecuteResult = (res: CellRenderResultData[]) => {
//   const binaryKeyCache: any = {};

//   return res.map((queryResult) => {
//     const { resultData } = queryResult;
//     const plainResultData = resultData.map((result) => {
//       if (typeof result !== 'object') return result;
//       const plainResult: { [key: string | symbol]: any } = {};
//       const keys = Object.keys(result);

//       for (let key of keys) {
//         const value = result[key].value;
//         plainResult[key] = value;

//         // 使用缓存来存储已经计算过的结果
//         if (!binaryKeyCache[key]) {
//           binaryKeyCache[key] = result[key].renderType?.includes('binary');
//         }

//         if (result[key].formatValue) {
//           plainResult[`get${key}`] = () => result[key].formatValue;
//         }

//         if (binaryKeyCache[key] && result[key]?.size) {
//           plainResult[`${key}CQSize`] = result[key].size;
//         }
//       }

//       return plainResult;
//     });

//     // const cleanData = JSON.parse(JSON.stringify(resultData));
//     return {
//       ...queryResult,
//       resultData: plainResultData,
//       detailedResultData: resultData,
//     };
//   });
// };

function sanitizeData(data: any) {
  try {
    // 粗暴但有效，能移除所有函数和循环引用
    return JSON.parse(JSON.stringify(data));
  } catch (e) {
    console.error("Sanitization failed", e);
    return data; // 出错时返回原始数据，避免应用崩溃
  }
}


export const formatExecuteResult = (res: CellRenderResultData[]) => {
  // 我们只修改 return 部分，保持前面的逻辑不变
  return res.map((queryResult) => {
    const { resultData } = queryResult; // resultData 是那个包含 CellData 的“重量级”原始数据
    
    // --- 这里是唯一的修改 ---
    
    // 1. 创建净化后的 detailedResultData
    // 这一步会创建一个全新的、不含任何函数或循环引用的数据副本
    const cleanDetailedResultData = sanitizeData(resultData);

    // 2. 在净化后的数据基础上，创建用于显示的 plainResultData
    // 注意：这里需要根据 sanitizeData 的行为调整，如果函数被移除了，
    // 我们需要直接从 cleanDetailedResultData 中取值。
    const plainResultData = cleanDetailedResultData.map((result: any) => {
      const plainResult: { [key: string | symbol]: any } = {};
      const keys = Reflect.ownKeys(result);
      for (let key of keys) {
         // 直接从净化后的数据中取值，不再创建函数
         plainResult[key] = result[key].value;
      }
      return plainResult;
    });

    console.log(cleanDetailedResultData)
    // 3. 返回一个全新的、干净的对象
    return {
      ...sanitizeData(queryResult), // 也净化一下外层，确保万无一失
      resultData: plainResultData,
      detailedResultData: cleanDetailedResultData,
    };
  });
};

export const getLimitedString = (
  data: Object | string | undefined,
  limitedLength: number = 50,
) => {
  if (!data) return ''
  const stringifyData = typeof data === 'string' ? data : JSON.stringify(data)
  if (!stringifyData) return ''
  const length = stringifyData.length
  return length > limitedLength
    ? `${stringifyData.slice(0, limitedLength)}...`
    : stringifyData
}

export const getColLimitLength = (colNum: number) => {
  const ROW_MAX_STRING_NUMBER = 1000
  const Col_MIN_STRING_NUMBER = 50
  return Col_MIN_STRING_NUMBER > ROW_MAX_STRING_NUMBER / colNum
    ? Col_MIN_STRING_NUMBER
    : Math.floor(ROW_MAX_STRING_NUMBER / colNum)
}

/** transform hex to string */
export const hexToString = (hex: string): string => {
  // 后端传过来的是不带空格的十六进制字符串
  if (!!hex) {
    let hexArray: string[] = []
    const length = hex.length
    for (let i = 0; i < length; i += 2) {
      hexArray.push(hex.slice(i, i + 2))
    }
    const hexArrayNumber = hexArray.map((item) => {
      return Number('0x' + item)
    })
    return String.fromCharCode(...hexArrayNumber)
  } else {
    return '??'
  }
}

// transfer string to hex
export const stringToHex = (str: string | number): string | number => {
  if(typeof(str) === 'number'){
    return str.toString(16)
  }
  if (str === '') return ''
  const hexCharCode = []
  // hexCharCode.push('0x')
  for (var i = 0; i < str.length; i++) {
    hexCharCode.push(str.charCodeAt(i).toString(16))
  }
  return hexCharCode.join('')
}

/**
 * 安全地处理行数据，避免创建持有原始数据引用的闭包函数
 * 这个函数解决了内存泄漏问题，通过提取值而不是创建闭包来处理 formatValue
 */
export const handleRowDataWithEditableSafe = (data: any[]) => {
  return data.map((curItem: any) => {
    const customCurItem: any = Object.assign({ editable: true }, {});
    const keys = Object.keys(curItem);

    for (const key of keys) {
      const value = curItem[key];

      // 如果包含 cursorValue，保留整个对象；否则只保留 value 字段
      if (value?.cursorValue !== undefined) {
        customCurItem[key] = value; // 保留完整对象以便访问 cursorValue
      } else {
        customCurItem[key] = value?.value;
      }

      // 关键修复：不创建闭包函数，直接提取 formatValue
      if (value?.formatValue) {
        // 不要这样做：customCurItem[`get${key}`] = () => value.formatValue;
        // 而是直接存储格式化后的值
        customCurItem[`${key}FormatValue`] = value.formatValue;

        // 为了兼容现有的 SimpleTextRenderer，我们创建一个不持有原始引用的函数
        const formatValueCopy = value.formatValue; // 提取值的副本
        customCurItem[`get${key}`] = () => formatValueCopy; // 闭包只引用副本，不引用原始对象
      }

      if (value?.renderType?.includes('binary') && value?.size) {
        customCurItem[`${key}CQSize`] = value.size;
      }

      if (!value?.editable) {
        customCurItem.editable = false;
      }
    }

    return customCurItem;
  });
}
