import React, { useState, useEffect, useMemo, useRef } from 'react'
import { SettingOutlined, RedoOutlined } from '@ant-design/icons'
import { Row, Col, Select, Radio, Button, Popconfirm, Input } from 'antd'
import { DatePicker, exportTaskCreatedNot } from 'src/components'
import dayjs, { Dayjs } from 'dayjs'
import {
  DataSourceType,
  getAuditUsers,
  SqlType,
  SqlTypes,
  ExtratTypes,
  ExecutionReportRequest,
  exportAuditLogSql,
} from 'src/api'
import styles from './index.module.scss'
import { formatDateRange } from 'src/util'
import { cloneDeep, debounce } from 'lodash'
import classNames from 'classnames'
import { SEARCH_ICON } from 'src/constants'
import SelectTreeData from '../permissionPage/modals/SelectTreeData'
import { useTranslation } from 'react-i18next'
import { useSelector } from 'src/hook'
const { RangePicker } = DatePicker
const { Option } = Select

interface FilterParams {
  executors?: string[]
  timeRange?: [number, number] | null
  dbTypes?: DataSourceType[]
  sqlTypes?: SqlType[]
  // 0 执行失败 1 执行成功
  resultFlag?: string
  executeSql?: string // 操作语句
  extratTypes?: ExtratTypes[]
  // 0所有 1执行编辑器 2终端
  actuatorType?: 0 | 1 | 2
  limit?: number
  offset?: number
  source?: string[] | null
  connIdsAlongWithDbType?: number[]
}

// 根据组件 filterParams 状态构造出查询请求所需要的参数
const getRequestParams = (filterParams: FilterParams) => {
  const { executors, timeRange, dbTypes, sqlTypes, resultFlag, extratTypes } = filterParams
  // 构造查询参数
  const params: Partial<ExecutionReportRequest> = {
    executeBeginMs: timeRange?.[0],
    executeEndMs: timeRange?.[1],
  }
  if (executors?.length) {
    params.executors = executors
  }
  if (dbTypes && dbTypes[0]) {
    params.dbTypes = dbTypes.map((dataSourceType) =>
      dataSourceType.toUpperCase(),
    ) as DataSourceType[]
  }
  if (sqlTypes && sqlTypes[0]) {
    params.sqlTypes = sqlTypes
  }
  if (resultFlag !== undefined) {
    params.resultFlag = Number(resultFlag)
  }
  if (extratTypes?.[0]) {
    params.extratTypes = extratTypes
  }
  delete filterParams.timeRange
  delete filterParams?.actuatorType
  return { ...filterParams, ...params }
}

export const SearchHeader = ({
  queryParams = {},
  setSearchParams,
  refresh,
  showCustomColumnPanel,
  isUnauthorized,
  sourceFiltersVal,
}: {
  queryParams: any;
  setSearchParams: (values: any) => void
  refresh: () => void
  showCustomColumnPanel: () => void
  isUnauthorized?: boolean
  sourceFiltersVal?: string[] | null
}) => {
  const { t } = useTranslation()
  const { locales } = useSelector(state => state.login)
  const [filterParams, setFilterParams] = useState<FilterParams>({})
  // 全部用户列表
  const [userList, setUserList] = useState<any[]>([])
  const [isExport, setIsExport] = useState(false);
  const inputRef = useRef<any>()

  const {
    executors,
    timeRange,
    dbTypes,
    sqlTypes,
    resultFlag,
    actuatorType,
    connIdsAlongWithDbType,
  } = filterParams

  const rangeValue = useMemo(() => {
    if (timeRange === null) {
      return null
    } else {
      const range = timeRange && timeRange.map((timestamp) => dayjs(timestamp)) as [
        Dayjs,
        Dayjs,
      ]
      return range
    }
  }, [timeRange])

  useEffect(() => {
    // 获取用户列表
    const getUserList = async () => {
      try {
        const users = await getAuditUsers()
        setUserList(users)
      } catch { }
    }
    getUserList()
  }, [])

  useEffect(() => {
    setFilterParams(queryParams)
  }, [queryParams])

  useEffect(() => {
    setFilterParams((val: any) => {
      return { ...val, source: sourceFiltersVal }
    })
  }, [sourceFiltersVal])

  const exportData = () => {
    setIsExport(true)
    exportAuditLogSql(getRequestParams(cloneDeep(filterParams)) as Partial<ExecutionReportRequest>).then((res: number) => {
      setIsExport(false);
      exportTaskCreatedNot();
    }).catch(() => {
      setIsExport(false)
    })
  }

  const SqlTypeOptions = SqlTypes.map((type) => ({ label: type, value: type }))

  // 模糊搜索的防抖操作
  const taskChange = debounce((value) => {
    setSearchParams({ ...filterParams, sqlKeyWord: value !== '' ? value : undefined, offset: 0 })
  }, 500)

  return (
    <div
      className={styles.searchHeader}
      style={locales === 'zh' ? { display: "flex" } : { display: "block" }}
    >
      <div className={styles.leftNode}>
        {
          !isUnauthorized ?
            <Radio.Group
              value={resultFlag}
              buttonStyle="solid"
              onChange={(e: any) =>
                setSearchParams({ ...filterParams, resultFlag: e.target.value, offset: 0 })
              }
            >
              <Radio.Button value={1}>{t("auays:rd_lbl.execution_success")}</Radio.Button>
              <Radio.Button value={0}>{t("auays:rd_lbl.execution_failure")}</Radio.Button>
            </Radio.Group> : <>&nbsp;</>
        }
        <Input
          prefix={SEARCH_ICON}
          className={classNames(styles.sqlInput, isUnauthorized && styles.overStep)}
          style={locales === 'en' ?
            { width: "380px", marginLeft: isUnauthorized ? "344px" : "8px" } :
            { width: "276px", marginLeft: isUnauthorized ? "178px" : "8px" }
          }
          ref={inputRef}
          allowClear
          placeholder={t("auays:inp_ph.ope_statement_audit_id")}
          onChange={(e: any) => taskChange(e.target.value)}
        />
      </div>
      <Row
        justify={locales === 'zh' ? 'end' : 'start'}
        gutter={16}
        style={locales === 'zh' ? { alignItems: 'center' } : { margin: "6px 0px 0px" }}
      >
        <label>{t("auays:div_lbl.filter_condition")}:</label>
        <Col>
          <Select
            mode="multiple"
            showArrow
            placeholder={t("auays:sele_ph.user")}
            value={executors}
            onChange={(selected) => {
              setFilterParams({ ...filterParams, executors: selected })
            }}
            maxTagCount={1}
            maxTagTextLength={10}
            allowClear
            style={{ minWidth: 144 }}
            optionFilterProp="label"
          >
            {userList.map((user) => {
              const { userId, userName } = user
              return (
                <Option
                  key={userId}
                  value={userId}
                  label={`${userName}(${userId})`}
                >
                  {`${userName}(${userId})`}
                </Option>
              )
            })}
          </Select>
        </Col>
        <Col>
          <SelectTreeData
            placeholder={t("auays:sele_ph.database_type_conn")}
            defaultValue={[
              ...dbTypes || [],
              ...connIdsAlongWithDbType?.map((id: number) => `connection:${id}`) || []
            ]}
            className={styles.selectTree}
            endInConnection={true}
            getNodePathValues={(values: any) => {
              let dbTypes: any[] = []
              let connIdsAlongWithDbType: number[] = []
              values.map((item: string) => {
                if (item.startsWith('connection:')) {
                  const splitArr = item.split(':')
                  connIdsAlongWithDbType.push(Number(splitArr[1]))
                } else {
                  dbTypes.push(item)
                }
              })
              setFilterParams({ ...filterParams, dbTypes, connIdsAlongWithDbType })
            }}
          />
        </Col>
        <Col>
          <Select
            showArrow
            placeholder={t("auays:sele_ph.statement_type")}
            value={sqlTypes}
            onChange={(sqlTypes) => {
              setFilterParams({ ...filterParams, sqlTypes })
            }}
            allowClear
            style={locales === 'en' ? { minWidth: 140 } : { minWidth: 96 }}
            mode="multiple"
            maxTagCount={1}
            options={SqlTypeOptions}
          ></Select>
        </Col>
        <Col>
          <Select
            showArrow
            placeholder={t("auays:sele_ph.executor_type")}
            value={actuatorType}
            onChange={(actuatorType: 0 | 1 | 2) => {
              setFilterParams({ ...filterParams, actuatorType })
            }}
            style={{ minWidth: 96 }}
          >
            <Option key="0" value={0}>
              {t("auays:exe_type.all")}
            </Option>
            <Option key="1" value={1}>
              {t("auays:exe_type.execution_editor")}
            </Option>
            <Option key="2" value={2}>
              {t("auays:exe_type.terminal_editor")}
            </Option>
          </Select>
        </Col>
        <Col>
          <RangePicker
            value={rangeValue}
            onChange={(dates) => {
              if (dates === null) {
                return setFilterParams({
                  ...filterParams,
                  timeRange: null,
                })
              }
              const [start, end] = formatDateRange(dates)
              if (start && end) {
                setFilterParams({
                  ...filterParams,
                  timeRange: [start, end],
                })
              }
            }}
            allowClear
            placeholder={[t("auays:rpk_ph.start_date"), t("auays:rpk_ph.end_date")]}
          />
        </Col>
        <Col>
          <Button
            onClick={() => setSearchParams({ ...filterParams, offset: 0 })}
            type="primary"
            className="mr8"
          >
            {t("auays:btn.query")}
          </Button>
          <Button
            onClick={() => {
              setFilterParams({
                executors: [],
                timeRange: null,
                actuatorType: 0,
                sqlTypes: [],
                dbTypes: [],
                resultFlag: undefined,
                connIdsAlongWithDbType: [],
                offset: 0,
                limit: 10,
                executeSql: undefined
              })
              inputRef.current.setValue('')
            }}
            className="mr8"
          >
            {t("auays:btn.reset")}
          </Button>
          <Popconfirm
            title={t("auays:pop_title.confirm_export")}
            onConfirm={exportData}
            placement="bottomRight"
          >
            <Button className="mr8" loading={isExport} disabled={isExport}>{t("auays:btn.export")}</Button>
          </Popconfirm>
          <Button
            icon={<RedoOutlined className={styles.refreshIcon} />}
            onClick={() => refresh()}
          />
          <Button
            className={styles.settingIcon}
            icon={<SettingOutlined />}
            type="primary"
            onClick={() => showCustomColumnPanel()}
          />
        </Col>
      </Row>
    </div>
  )
}
