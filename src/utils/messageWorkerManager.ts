/**
 * Web Worker Manager for Message Processing
 * 消息处理Web Worker管理器
 */

interface WorkerMessage {
  type: string;
  requestId: string;
  payload: any;
}

interface ProcessMessageRequest {
  unreadMessageByUserId: any;
  userId: string;
}

interface ProcessMessageResponse {
  success: boolean;
  data: any[];
  processingTime: number;
  itemCount: number;
  originalTotal: number;
  userId: string;
  error?: string;
}

class MessageWorkerManager {
  private worker: Worker | null = null;
  private requestId = 0;
  private pendingRequests = new Map<string, {
    resolve: (value: any) => void;
    reject: (reason: any) => void;
    timeout: NodeJS.Timeout;
  }>();
  private isSupported = false;
  private isInitialized = false;

  constructor() {
    this.isSupported = typeof Worker !== 'undefined';
    if (this.isSupported) {
      this.initWorker();
    }
  }

  private initWorker() {
    try {
      this.worker = new Worker('/workers/messageProcessor.js');
      this.worker.onmessage = this.handleWorkerMessage.bind(this);
      this.worker.onerror = this.handleWorkerError.bind(this);
      this.isInitialized = true;
      
      // 发送健康检查
      this.ping().catch(console.warn);
    } catch (error) {
      console.warn('Failed to initialize message worker:', error);
      this.isSupported = false;
    }
  }

  private handleWorkerMessage(event: MessageEvent<WorkerMessage>) {
    const { type, requestId, payload } = event.data;
    const request = this.pendingRequests.get(requestId);

    if (!request) {
      console.warn('Received response for unknown request:', requestId);
      return;
    }

    clearTimeout(request.timeout);
    this.pendingRequests.delete(requestId);

    switch (type) {
      case 'MESSAGES_PROCESSED':
        request.resolve(payload);
        break;
      case 'PONG':
        request.resolve(payload);
        break;
      case 'ERROR':
        request.reject(new Error(payload.error));
        break;
      default:
        request.reject(new Error(`Unknown response type: ${type}`));
    }
  }

  private handleWorkerError(error: ErrorEvent) {
    console.error('Worker error:', error);
    
    // 清理所有待处理的请求
    this.pendingRequests.forEach(({ reject, timeout }) => {
      clearTimeout(timeout);
      reject(new Error('Worker error occurred'));
    });
    this.pendingRequests.clear();
  }

  private generateRequestId(): string {
    return `req_${++this.requestId}_${Date.now()}`;
  }

  private sendMessage(type: string, payload: any, timeoutMs = 5000): Promise<any> {
    return new Promise((resolve, reject) => {
      if (!this.worker || !this.isInitialized) {
        reject(new Error('Worker not available'));
        return;
      }

      const requestId = this.generateRequestId();
      const timeout = setTimeout(() => {
        this.pendingRequests.delete(requestId);
        reject(new Error('Worker request timeout'));
      }, timeoutMs);

      this.pendingRequests.set(requestId, { resolve, reject, timeout });

      this.worker.postMessage({ type, requestId, payload });
    });
  }

  /**
   * 处理消息数据
   */
  async processMessages(request: ProcessMessageRequest): Promise<ProcessMessageResponse> {
    if (!this.isSupported || !this.isInitialized) {
      // Fallback to main thread processing
      return this.fallbackProcessMessages(request);
    }

    try {
      return await this.sendMessage('PROCESS_MESSAGES', request);
    } catch (error) {
      console.warn('Worker processing failed, falling back to main thread:', error);
      return this.fallbackProcessMessages(request);
    }
  }

  /**
   * 主线程fallback处理
   */
  private fallbackProcessMessages(request: ProcessMessageRequest): ProcessMessageResponse {
    const startTime = performance.now();
    const { unreadMessageByUserId, userId } = request;

    try {
      if (!unreadMessageByUserId?.data) {
        return {
          success: true,
          data: [],
          processingTime: 0,
          itemCount: 0,
          originalTotal: unreadMessageByUserId?.total || 0,
          userId
        };
      }

      const formattedData = unreadMessageByUserId.data.map((item: any) => {
        const { 
          notificationVoExtra = {}, 
          extraAttrsJson = {}, 
          businessId, 
          category, 
          senderId, 
          receiverId 
        } = item;

        return {
          ...item,
          ...notificationVoExtra,
          ...extraAttrsJson,
          alarmType: extraAttrsJson?.alarmType,
          flowTaskId: extraAttrsJson?.taskId,
          param: extraAttrsJson?.alarmParam || {},
          applyId: businessId,
          msgId: businessId,
          msgType: category,
          sender: senderId,
          userId: [receiverId]
        };
      });

      const endTime = performance.now();
      
      return {
        success: true,
        data: formattedData,
        processingTime: endTime - startTime,
        itemCount: formattedData.length,
        originalTotal: unreadMessageByUserId?.total || 0,
        userId
      };
    } catch (error) {
      return {
        success: false,
        data: [],
        processingTime: performance.now() - startTime,
        itemCount: 0,
        originalTotal: unreadMessageByUserId?.total || 0,
        userId,
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }

  /**
   * 健康检查
   */
  async ping(): Promise<any> {
    if (!this.isSupported || !this.isInitialized) {
      return { status: 'fallback', timestamp: Date.now() };
    }

    try {
      return await this.sendMessage('PING', {}, 1000);
    } catch (error) {
      return { status: 'error', error: error instanceof Error ? error.message : 'Unknown error' };
    }
  }

  /**
   * 获取状态信息
   */
  getStatus() {
    return {
      isSupported: this.isSupported,
      isInitialized: this.isInitialized,
      pendingRequests: this.pendingRequests.size,
      workerAvailable: !!this.worker
    };
  }

  /**
   * 销毁worker
   */
  destroy() {
    if (this.worker) {
      // 清理所有待处理的请求
      this.pendingRequests.forEach(({ reject, timeout }) => {
        clearTimeout(timeout);
        reject(new Error('Worker is being destroyed'));
      });
      this.pendingRequests.clear();

      this.worker.terminate();
      this.worker = null;
      this.isInitialized = false;
    }
  }
}

// 创建单例实例
export const messageWorkerManager = new MessageWorkerManager();

// 导出类型
export type { ProcessMessageRequest, ProcessMessageResponse };
