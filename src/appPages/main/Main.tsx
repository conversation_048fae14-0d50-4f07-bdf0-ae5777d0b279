import React, { useEffect, useState, useRef, useMemo } from 'react'
import { useSelector, useDispatch, useSessionPolling, useTick, useRequest } from 'src/hook'
import { useWebsocket } from 'src/features/websocket'
import { DndProvider } from 'react-dnd'
import { HTML5Backend } from 'react-dnd-html5-backend'
import { HeaderNav } from './Header'
import {
  QueryPoolContext,
  QueryPoolPane,
  QueryPool,
  ErrorBoundary,
  Guide
} from 'src/components'
import dayjs from 'dayjs'
import 'dayjs/locale/zh-cn'
import 'src/styles/ag-grid.scss'
import styles from './index.module.scss'
import { Route, Switch, Redirect, useLocation, useHistory } from 'react-router-dom'
import { FlowFormModals } from 'src/pageTabs/flowPages'
import { ResultLogModals } from 'src/pageTabs/queryPage/resultTabs/resultLogs/ResultLogModals'
import { Input, Form, Modal, Button } from 'antd'
import { 
  changeUserPasswordFirstLogin, 
  getForcedBindingByUser, 
  getSettingType, 
  getUserConfig, 
  ifBase64Encode, 
  userLogout, 
  getSysPasswordPolicy, 
  getWatermarkEffectSetting, 
  getExportSetting, 
  getApplyExportAutoDownloadSetting
} from 'src/api'
import { getAndSetDataSourceList } from 'src/store/extraSlice/dataSourceSlice'
import { persistor } from 'src/store'
import { passwordSpecialCharacterValidator, passwordValidator, passwordValidatorSelf } from 'src/util/nameValidator'
import { UpdateVersion } from 'src/components'
import { RouterMenus } from "src/projectConfig/RouterMenus";
import LoginBindPhoneModal from './LoginBindPhoneModal';
import SceneGuideModal from 'src/pageTabs/SceneGuideModal';
import { ConfirmLogoutModal } from 'src/components/ConfirmLogoutModal'
import { BindOtpModal } from 'src/components/BindOtpModal'
import { showModal } from 'src/store/extraSlice/modalVisibleSlice'
import { setIsBase64Encode, setShowCustomAudit, setIsLoginCtrlByManager, setWatermarkEffect, IExportSetting, setExportSetting, setExportAutoDownload } from '../login/loginSlice'
import { LoginBindOtpModal } from './LoginBindOtpModal'
import { AlarmMessagesModal } from 'src/components/AlarmMessagesModal'
import PageTabs from './pageTabs'
import { useTranslation } from 'react-i18next';
import i18next from 'i18next';

dayjs.locale('zh-cn')

export const Main: React.FC = () => {
  const history = useHistory()
  const { t } = useTranslation();
  const { data: settingData, loading, run } = useRequest(getSysPasswordPolicy, { manual: true },)
  const visibleLogin = useSelector((state) => state.modal.ConfirmLogoutModal?.visible || false)
  const { permissionMenus } = useSelector(
      (state) => state.login.userInfo
    );
  const [data, setData] = useState<any>()

  const { theme } = useSelector((state) => state.appearance)
  const [visible, setvisible] = useState(false)
  //是否强制绑定手机
  const [needToBindPhone, setNeedToBindPhone] = useState(false)
  const [forceBindPhone, setForceBindPhone] = useState<boolean>(false)
  //是否强制绑定OTP
  const [needToBindOtp, setNeedToBindOtp] = useState(false)
  const [forceBindOtp, setForceBindOtp] = useState<boolean>(false)
  const location = useLocation();
  const [form] = Form.useForm()
  const dispatch = useDispatch()

  // 手动设置首页路由
  useEffect(()=>{
    const indexUrl = ['/', '/system_home']
    if(indexUrl.includes(location.pathname)){
      // 跳转有权限的首个菜单
      const curPath = "/system_home" ;
      if (location.pathname !== curPath) { 
          history.push(curPath);
      }
    }
  // eslint-disable-next-line react-hooks/exhaustive-deps
  },[history, location.pathname])

  useEffect(() => {
    run().then(res => {
      // res.systemPasswordStrong = false;
      setData(res);
    })
  }, [run])

  useEffect(() => {
    window.document.body.setAttribute('data-theme', theme)
  }, [theme])

  const { firstLogin, userId, globalOtpIsOpenAndNoBind, ifHaveShowGuide, alarmMessages, passwordValid = true } = useSelector(
    (state) => state.login.userInfo,
  ) as any

  /** 查看全局开启状态 */
  const { run: getForcedBindingByUserRun } = useRequest(getForcedBindingByUser, {
    manual: true,
    onSuccess: (res) => {
      setForceBindPhone(res?.forcePhoneBing)
      setForceBindOtp(res?.forceOtpBing)
    }
  })

  /** 调用个人设置接口判断是否展示自定义审计指标 */
  useRequest(getUserConfig, {
    manual: false,
    onSuccess: ({ customAudit }) => {
      dispatch(setShowCustomAudit(customAudit ?? false))
    },
  })

  /** 系统设置-是否自动下载文件 消息通知轮询接口是否 */
  useRequest(getApplyExportAutoDownloadSetting, {
    manual: false,
    onSuccess: ( exportAutoDownload ) => {
      dispatch(setExportAutoDownload(exportAutoDownload ?? false))
    },
  })

  /** 获取全局水印效果设置 */
  useRequest(getWatermarkEffectSetting, {
    manual: false,
    onSuccess: (data) => {
      const props = {
        fontSize: Number(data?.fontSize),
        gapX: Number(data?.horizontalGap),
        gapY: Number(data?.verticalGap),
        fontColor: data?.colorValue,
        opacity: Number(data?.transparency)
      }
      dispatch(setWatermarkEffect(props))
    },
    refreshDeps: [location?.pathname]
  })

  /** 获取工具权限-导出设置 */
  useRequest(getExportSetting, {
    manual: false,
    onSuccess: (data: IExportSetting) => {
      dispatch(setExportSetting(data))
    },
    refreshDeps: [location?.pathname]
  })

  /** 判断用户的登录认证方式是否为管理员自定义 */
  useRequest(getSettingType, {
    manual: false,
    onSuccess: ({ loginSetting }) => {
      dispatch(setIsLoginCtrlByManager(loginSetting === 'ADMIN_CUSTOM'))
    }
  })

  useEffect(() => {
    if (firstLogin || !passwordValid) {
      setvisible(true)
      //先强制改密 在强制绑定手机号
    } else if (!firstLogin) {
      getForcedBindingByUserRun().then((res) => {
        if (!firstLogin && res?.forcePhoneBing && !visible) {
          setNeedToBindPhone(true)
        }
        if (!firstLogin && !needToBindPhone && !res?.forcePhoneBing && res?.forceOtpBing) {
          setNeedToBindOtp(true)
        }
      })
    }
    //改密后首次登录显示场景引导
    if (ifHaveShowGuide) {
      dispatch(showModal('ModalSceneGuide'));
    }
  }, [firstLogin, passwordValid, forceBindOtp, ifHaveShowGuide, forceBindPhone, getForcedBindingByUserRun, needToBindPhone, visible])

  useEffect(() => {
    // 获取数据源列表
    dispatch(getAndSetDataSourceList())
  }, [dispatch])

  useEffect(() => {
    if (globalOtpIsOpenAndNoBind) {
      dispatch(showModal('BindOtpModal'))
    }
  }, [globalOtpIsOpenAndNoBind])

  useEffect(() => {
    // license 证书过期提示 (数组类型)11
    if (alarmMessages?.length > 0) {
      dispatch(showModal('AlarmMessagesModal'))
    }
  }, [alarmMessages, dispatch])

  // 判断传sql时是否需要编码
  useEffect(() => {
    ifBase64Encode().then((res) => {
      dispatch(setIsBase64Encode(res))
    }).catch((err) => {
      dispatch(setIsBase64Encode(false))
    })
  }, [])

  const onKeyUp = (e: any) => {
    if (e.keyCode === 13) {
      fixPassword()
    }
  }
  const fixPassword = async () => {
    const values = await form.validateFields()
    if (userId) {
      try {
        await changeUserPasswordFirstLogin({
          userId,
          newP: values.newP,
        })

        setvisible(false)
        userLogout().then(async () => {
          persistor.purge()
        })
      } catch (error) { }
    }
  }

  // 建立 websocket 连接
  // 集群暂时无法处理websocket，所以使用轮询替代
  // useWebsocket()

  // 设置登出倒计时
  useTick()

  // !HACK 执行时轮询维持告诉后端维持 session
  //useSessionPolling()

  // !HACK query pool 暂时拿到顶层
  // !真正解决需要顶层事件响应

  // 分块查询池 generator map
  const poolMapRef = useRef<Map<string, QueryPool>>(new Map())
  const pathName = window.location.pathname

  const renderForm = useMemo(() => {
    if ([true, false].includes(data?.systemPasswordStrong)) {
      return (
        <>
          <Form form={form} labelCol={{ span: i18next.language === 'en' ? 10 : 5 }} labelAlign="left">
            <div style={{ marginBottom: "16px" }}>
              { passwordValid === false ?
                <span>{t("main:passwordExpired")}</span>
                : <span>{t("main:firstTimeLogin")}</span>
              }
            </div>
            <Form.Item
              label={t("main:newPassword")}
              name="newP"
              required
              rules={
                data?.systemPasswordStrong ? [{ validator: passwordValidator }, { validator: passwordSpecialCharacterValidator }]
                  :
                  [{
                    validator(_rule, value, callback) {
                      passwordValidatorSelf(_rule, value, callback,
                        {
                          passwordMax: data?.passwordMax || 16,
                          passwordMin: data?.passwordMin || 9,
                          containDigits: data?.containDigits,
                          containUpperLetters: data?.containUpperLetters,
                          containLowerLetters: data?.containLowerLetters,
                          containSymbols: data?.containSymbols,
                        })
                    },
                  }]
              }
            >
              <Input.Password onKeyUp={onKeyUp} />
            </Form.Item>
            <Form.Item
              label={t("main:confirmNewPassword")}
              name="confirm"
              dependencies={["newP"]}
              rules={[
                { required: true, message: t("main:pleaseConfirmNewPassword") },
                ({ getFieldValue }) => ({
                  validator(_rule, value) {
                    if (!value || getFieldValue("newP") === value) {
                      return Promise.resolve();
                    }
                    return Promise.reject(t("main:passwordsDoNotMatch"));
                  },
                }),
              ]}
            >
              <Input.Password />
            </Form.Item>
          </Form>
        </>
      )
    }
  }, [data, form, onKeyUp]);

  return (
    <div className={styles.app}>
      <QueryPoolContext.Provider value={poolMapRef.current}>
        {/* 引导 */}
        <Guide />
        <DndProvider backend={HTML5Backend}>
          {pathName !== "/waitlogin" && (
            <header className={styles.appHeader}>
              <HeaderNav />
            </header>
          )}
          <ErrorBoundary>
            <main className={styles.appContent}>
              {/* 页面tab标签 */}
              <PageTabs />
              {/* 路由处理 */}
              <Switch>
                {RouterMenus.map(({ key }) => (
                  <Route path={key} key={key} />
                ))}
                <Redirect to="/system_home"></Redirect>
              </Switch>
            </main>
          </ErrorBoundary>
        </DndProvider>
        <Modal
          title={t("main:modifyPassword")}
          visible={visible}
          closable={false}
          footer={
            <div>
              <Button
                style={{}}
                type="primary"
                onClick={() =>
                  userLogout().then(async () => {
                    persistor.purge();
                  })
                }
              >
                {t("main:exit")}
              </Button>
              <Button type="primary" onClick={() => fixPassword()}>
                {t("main:confirm")}
              </Button>
            </div>
          }
        >
          {renderForm}
        </Modal>
        <LoginBindPhoneModal visible={needToBindPhone} closePhoneModal={() => setNeedToBindPhone(false)} />
        <LoginBindOtpModal visible={needToBindOtp} closeOtpModal={() => setNeedToBindOtp(false)} />
        <FlowFormModals />
        <ResultLogModals />
        {/* !HACK query pool effects */}
        <QueryPoolPane />
        <UpdateVersion />
        {visibleLogin && <ConfirmLogoutModal />}
        <BindOtpModal userId={userId} />
        {/* 新手场景向导 */}
        <SceneGuideModal />
        {/* license 证书过期提示 */}
        <AlarmMessagesModal />
      </QueryPoolContext.Provider>
    </div>
  );
}
