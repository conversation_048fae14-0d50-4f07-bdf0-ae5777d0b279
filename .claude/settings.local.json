{"permissions": {"allow": ["Bash(grep:*)"], "deny": []}, "environment": {"WORKSPACE": "/Users/<USER>/code/cq/cq-persist"}, "experimentalTools": {"notify": {"commandAfterRun": "bash /Users/<USER>/code/cq/cq-persist/hooks/notify.sh", "commandAfterUserInput": "bash /Users/<USER>/code/cq/cq-persist/hooks/notify.sh input"}}, "experimentalHooks": {"preToolUse": "bash /Users/<USER>/code/cq/cq-persist/hooks/mcp-security-scan.sh", "preToolUse_gemini": "bash /Users/<USER>/code/cq/cq-persist/hooks/gemini-context-injector.sh", "preToolUse_task": "bash /Users/<USER>/code/cq/cq-persist/hooks/subagent-context-injector.sh"}}