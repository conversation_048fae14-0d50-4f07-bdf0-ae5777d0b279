/**
 * Web Worker for processing message data
 * 处理消息轮询中的复杂数据格式化计算
 */

// 消息数据格式化处理函数
function formatMessageData(unreadMessageByUserId) {
  if (!unreadMessageByUserId?.data) {
    return [];
  }

  const startTime = performance.now();
  
  try {
    const formattedData = unreadMessageByUserId.data.map((item) => {
      const { 
        notificationVoExtra = {}, 
        extraAttrsJson = {}, 
        businessId, 
        category, 
        senderId, 
        receiverId 
      } = item;

      return {
        ...item,
        ...notificationVoExtra,
        ...extraAttrsJson,
        alarmType: extraAttrsJson?.alarmType,
        flowTaskId: extraAttrsJson?.taskId,
        param: extraAttrsJson?.alarmParam || {},
        applyId: businessId,
        msgId: businessId,
        msgType: category,
        sender: senderId,
        userId: [receiverId] // 原数据结构返回为数组，此处改为字符串
      };
    });

    const endTime = performance.now();
    const processingTime = endTime - startTime;

    return {
      success: true,
      data: formattedData,
      processingTime,
      itemCount: formattedData.length
    };
  } catch (error) {
    return {
      success: false,
      error: error.message,
      data: []
    };
  }
}

// 监听主线程消息
self.onmessage = function(event) {
  const { type, payload, requestId } = event.data;

  switch (type) {
    case 'PROCESS_MESSAGES':
      const result = formatMessageData(payload.unreadMessageByUserId);
      
      // 发送处理结果回主线程
      self.postMessage({
        type: 'MESSAGES_PROCESSED',
        requestId,
        payload: {
          ...result,
          originalTotal: payload.unreadMessageByUserId?.total || 0,
          userId: payload.userId
        }
      });
      break;

    case 'PING':
      // 健康检查
      self.postMessage({
        type: 'PONG',
        requestId,
        payload: { status: 'healthy', timestamp: Date.now() }
      });
      break;

    default:
      self.postMessage({
        type: 'ERROR',
        requestId,
        payload: { error: `Unknown message type: ${type}` }
      });
  }
};

// 错误处理
self.onerror = function(error) {
  self.postMessage({
    type: 'ERROR',
    payload: { 
      error: error.message,
      filename: error.filename,
      lineno: error.lineno 
    }
  });
};
